import { Component, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-quote-search',
  template: `
    <div class="search-card">
      <!-- Filter and Search Section -->
      <div class="row1">
        <!-- Input for number, reference -->
        <div class="row1_col1">
          <label for="search-input">Quote Number or Customer</label>
          <div class="input-container">
            <input 
              type="text" 
              class="search-input" 
              id="search-input" 
              [value]="searchTerm"
              (input)="onSearchTermChange($event)"
              placeholder="Enter quote number or customer name"
              [attr.aria-label]="'Search by quote number or customer name'" />
            <i class="bi bi-search" aria-hidden="true"></i>
          </div>
        </div>

        <div class="row1_col3">
          <label for="StartDate">Start Date</label>
          <input 
            type="date" 
            class="date-picker" 
            id="StartDate" 
            [value]="startDate || ''"
            (change)="onStartDateChange($event)"
            [attr.aria-label]="'Filter by start date'" />
        </div>

        <div class="row1_col4">
          <label for="EndDate">End Date</label>
          <input 
            type="date" 
            class="date-picker" 
            id="EndDate" 
            [value]="endDate || ''"
            (change)="onEndDateChange($event)"
            [attr.aria-label]="'Filter by end date'" />
        </div>
      </div>

      <div class="row2">
        <div class="row2_col3">
          <button 
            type="button" 
            class="secondary-button" 
            (click)="onReset()"
            [attr.aria-label]="'Reset all filters'">
            Reset
          </button>
        </div>
        <div class="row2_col1">
          <button 
            type="button" 
            class="primary-button" 
            (click)="onSearch()"
            [attr.aria-label]="'Apply search filters'">
            Search
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .search-card {
      background: white;
      border-radius: 8px;
      padding: 1.5rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      margin-bottom: 1rem;
    }
    
    .row1 {
      display: grid;
      grid-template-columns: 2fr 1fr 1fr;
      gap: 1rem;
      margin-bottom: 1rem;
    }
    
    .row2 {
      display: flex;
      justify-content: flex-end;
      gap: 1rem;
    }
    
    .input-container {
      position: relative;
    }
    
    .input-container i {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      color: #6c757d;
    }
    
    .search-input, .date-picker {
      width: 100%;
      padding: 0.5rem;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 0.875rem;
    }
    
    .search-input {
      padding-right: 2.5rem;
    }
    
    label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #333;
    }
    
    .primary-button, .secondary-button {
      padding: 0.5rem 1.5rem;
      border-radius: 4px;
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    
    .primary-button {
      background-color: #007bff;
      color: white;
      border: 1px solid #007bff;
    }
    
    .primary-button:hover {
      background-color: #0056b3;
      border-color: #0056b3;
    }
    
    .secondary-button {
      background-color: white;
      color: #6c757d;
      border: 1px solid #6c757d;
    }
    
    .secondary-button:hover {
      background-color: #f8f9fa;
    }
    
    @media (max-width: 768px) {
      .row1 {
        grid-template-columns: 1fr;
      }
      
      .row2 {
        flex-direction: column;
      }
    }
  `]
})
export class QuoteSearchComponent {
  @Input() searchTerm: string = '';
  @Input() startDate: string | null = null;
  @Input() endDate: string | null = null;
  
  @Output() searchTermChange = new EventEmitter<string>();
  @Output() startDateChange = new EventEmitter<string | null>();
  @Output() endDateChange = new EventEmitter<string | null>();
  @Output() search = new EventEmitter<void>();
  @Output() reset = new EventEmitter<void>();

  onSearchTermChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.searchTermChange.emit(target.value);
  }

  onStartDateChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.startDateChange.emit(target.value || null);
  }

  onEndDateChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.endDateChange.emit(target.value || null);
  }

  onSearch(): void {
    this.search.emit();
  }

  onReset(): void {
    this.reset.emit();
  }
}
