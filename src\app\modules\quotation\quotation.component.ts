import { <PERSON><PERSON><PERSON>w<PERSON><PERSON><PERSON>, <PERSON>mponent, <PERSON><PERSON><PERSON><PERSON>, HostL<PERSON>ener, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { QuoteHead } from './quotation';
import { QuotationService } from './quotation.service';
import { Router } from '@angular/router';
import Swal from 'sweetalert2';
import { DomSanitizer } from '@angular/platform-browser';
import { fromEvent, Subscription } from 'rxjs';
import { BusinessPartnerService } from '../business-partner/business-partner.service';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import {  EntityService } from '../entity/entity.service';
import { Entity, EntityTradingName } from '../entity/entity';
import { EmailTemplate, EmailTemplateService } from '../settings/email-template.service';
import { NgForm } from '@angular/forms';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import { InvoiceHead } from '../invoice/invoice';
import { Modal } from 'bootstrap';
import { SwalAlertsService } from '../swal-alerts/swal-alerts.service';
import * as bootstrap from 'bootstrap';
import { QuotationConstants, QuoteStatus, UserRole, EmailType, QuoteStatusUtils } from './quotation.constants';
import { EmailData, FilterCriteria, UserInfo, ValidationResult } from './quotation.interfaces';
import { QuotationValidationService } from './services/quotation-validation.service';
import { UserPermissionService } from './services/user-permission.service';
import { QuotationEmailService } from './services/quotation-email.service';
import { QuotationBusinessService } from './services/quotation-business.service';
@Component({
  selector: 'app-quotation',
  templateUrl: './quotation.component.html',
  styleUrls: ['./quotation.component.css'],
})
export class QuotationComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  @ViewChild('sendQuote') sendQuote!: NgForm;

  // Expose utility functions to template
  QuoteStatusUtils = QuoteStatusUtils;
  
  entity: Entity = new Entity();
  [x: string]: any;
  quotes: QuoteHead[] = []; 
  invoices: InvoiceHead[] = [];
  searchTerm: string = ''; 
  filteredQuotes: QuoteHead[] = [];
  selectedQuotes: Set<QuoteHead> = new Set<QuoteHead>();
  entityTradingName: EntityTradingName = new EntityTradingName();
  quoteHeadList: QuoteHead[] = [];
  isAllSelected: boolean = false;
  quotationData: QuoteHead = new QuoteHead();
  activeTab = 'all';
  private audio!: HTMLAudioElement;
  emailTemplates: any[] = [];
  quoteTemplate: any = null; 
  subject: string = '';
  content: string = '';
  templateHtmlContent: string = '';
  recipientEmail: string = ''; // Loaded from the database
  TempRecipientEmail: string ='';

  startDate: string | null = null; // Bind to the start date input
  endDate: string | null = null;   // Bind to the end date input

  constructor(
    private quotationService: QuotationService,
    private emailTemplateService: EmailTemplateService,
    private entityService: EntityService,
    private businessPartnerService: BusinessPartnerService,
    private router: Router,
    private swalAlertsService: SwalAlertsService,
    public sanitizer: DomSanitizer,
    private validationService: QuotationValidationService,
    private userPermissionService: UserPermissionService,
    private emailService: QuotationEmailService,
    private businessService: QuotationBusinessService) { }

  ngOnInit() {
    this.fetchQuotations();
  }

  modalHideSubscription?: Subscription;

  //dropdown
  isDropdownOpen = false;

  @ViewChild('dropdownRef') dropdownRef!: ElementRef;

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  closeDropdown() {
    this.isDropdownOpen = false;
  }

  // Listen for clicks on the whole document
  @HostListener('document:click', ['$event'])
  handleClickOutside(event: MouseEvent) {
    if (
      this.dropdownRef && !this.dropdownRef.nativeElement.contains(event.target) 
    ) {
      this.closeDropdown();
    }
  }

  ngAfterViewInit() {
    const modalElement = document.getElementById('quotePreviewModal');
    if (modalElement) {
      this.modalHideSubscription = fromEvent(modalElement, 'hide.bs.modal').subscribe(() => {
        this.clearSelectedCheckboxes();
      });
    }
  }

 loadRecipientEmail(quoteHead: QuoteHead): void {
    if (quoteHead.businessPartnerId) {
      this.businessPartnerService.getBusinessPartnerById(quoteHead.businessPartnerId).subscribe(
        (businessPartner) => {
          if (businessPartner && businessPartner.email) {
            this.recipientEmail = businessPartner.email;
            this.TempRecipientEmail = businessPartner.email;
          } else {
            this.recipientEmail = ''; // Clear if no email found
            this.TempRecipientEmail = '';
            console.warn('No email found for the selected business partner.');
          }
        },
        (error) => {
          console.error('Error fetching recipient email:', error);
          this.recipientEmail = '';
          this.TempRecipientEmail = '';
        }
      );
    }
  }


loadEmailTemplate(): void {
  const entityId = +((localStorage.getItem(QuotationConstants.STORAGE_KEYS.ENTITY_ID)) + "");

  this.emailTemplateService.getEmailTemplateByEntityId(entityId).subscribe(
    (data: EmailTemplate[]) => {
      if (data && data.length > 0) {
        this.emailTemplates = data;
        this.quoteTemplate = this.emailTemplates.find(template => template.emailType === EmailType.QUOTE);

        if (this.quoteTemplate) {
          const selectedQuotes = this.quotes.filter(quote => quote.selected);

          const parser = new DOMParser();
         // const year = new Date().getFullYear();
          const today = new Date();
          const currentYear = today.getFullYear().toString();
                 
          if (selectedQuotes.length === 1) {
            const selectedQuote = selectedQuotes[0];
            this.subject = this.quoteTemplate.subject.replace(QuotationConstants.TEMPLATE_PLACEHOLDERS.QUOTE_NUMBER, selectedQuote.quoteNumber);
            this.loadRecipientEmail(selectedQuote);

            this.businessPartnerService.getBusinessPartnerById(selectedQuote.businessPartnerId).subscribe(
              (bp) => {
                const bpName = bp?.bpName || QuotationConstants.DEFAULTS.CUSTOMER_NAME;
                const quoteNumber = selectedQuote.quoteNumber || QuotationConstants.DEFAULTS.QUOTE_NUMBER;


                this.templateHtmlContent = this.quoteTemplate.content
                  .replace(QuotationConstants.TEMPLATE_PLACEHOLDERS.BUSINESS_PARTNER_NAME, bpName)
                  .replace(QuotationConstants.TEMPLATE_PLACEHOLDERS.QUOTE_NUMBER, quoteNumber)
                  .replace(QuotationConstants.TEMPLATE_PLACEHOLDERS.CURRENT_YEAR, currentYear);


                const parsed = parser.parseFromString(this.templateHtmlContent, 'text/html');
                this.content = parsed.body.textContent || "";
              },
              (error) => {
                console.error('Failed to load business partner info for quote', error);
              }
            );
          } else if (selectedQuotes.length > 1) {
            this.subject = QuotationConstants.DEFAULTS.MULTIPLE_QUOTES_SUBJECT;
            this.templateHtmlContent = this.quoteTemplate.content
              .replace(QuotationConstants.TEMPLATE_PLACEHOLDERS.BUSINESS_PARTNER_NAME, QuotationConstants.DEFAULTS.CUSTOMER_NAME)
              .replace(QuotationConstants.TEMPLATE_PLACEHOLDERS.QUOTE_NUMBER, QuotationConstants.DEFAULTS.MULTIPLE_QUOTES_QUOTE_NUMBER)
              .replace(QuotationConstants.TEMPLATE_PLACEHOLDERS.CURRENT_YEAR, currentYear);

            const parsed = parser.parseFromString(this.templateHtmlContent, 'text/html');
            this.content = parsed.body.textContent || "";
          } else {
            this.subject = '';
            this.templateHtmlContent = '';
            this.content = '';
          }
        }
      }
    }
  );
}


handleSendQuoteClick(): void {
  const selectedQuotes = this.businessService.getSelectedQuotes(this.filteredQuotes);

  const validation = this.validationService.validateQuotesForSending(selectedQuotes);
  if (!validation.isValid) {
    this.swalAlertsService.showWarning(validation.message!, () => {});
    return;
  }

  const validStatusQuotes = this.businessService.getValidQuotesForSending(selectedQuotes);
  const invalidStatusQuotes = selectedQuotes.filter(q =>
    !QuoteStatusUtils.isValidForSending(q.status)
  );

  if (invalidStatusQuotes.length > 0) {
    const invalidIds = invalidStatusQuotes.map(q => q.quoteId).join(', ');
    Swal.fire({
      title: 'Some quotes skipped',
      html: `The following quotes have invalid status and will be skipped:<br><strong>${invalidIds}</strong>`,
      icon: 'warning',
      confirmButtonText: 'Continue',
      confirmButtonColor: '#007bff'
    }).then(() => {
  
      this.loadEmailTemplate();

    

        const modalElement = document.getElementById(QuotationConstants.MODAL_IDS.SEND_QUOTE);
        if (modalElement) {
          const sendModal = new Modal(modalElement);
          sendModal.show();
        } else {
          console.error(`${QuotationConstants.MODAL_IDS.SEND_QUOTE} element not found.`);
        }
  
    });

  } else {
    // No invalid quotes — continue as usual
    
    this.loadEmailTemplate();

   
      const modalElement = document.getElementById(QuotationConstants.MODAL_IDS.SEND_QUOTE);
      if (modalElement) {
        const sendModal = new Modal(modalElement);
        sendModal.show();
      } else {
        console.error(`${QuotationConstants.MODAL_IDS.SEND_QUOTE} element not found.`);
      }

  }
}



@ViewChild('closePreview') closePreview: any;
@ViewChild('closeSendQuote') closeSendQuote: any;
isSending: boolean = false;
sendSelectedQuotes(): void {
  const userString = localStorage.getItem(QuotationConstants.STORAGE_KEYS.USER);
      if (!userString) {
        // Handle case where user is not found
        return;
      }

      const currentUser: UserInfo = JSON.parse(userString);
      const userRole = currentUser.roleName;
      const userId = currentUser.id;

      if (userRole === UserRole.FREE) {
        const currentDate = new Date();
        const currentMonth = currentDate.getMonth();
        const currentYear = currentDate.getFullYear();

        // ✅ Count invoices created by this user with status "Sent" this month
        const sentInvoicesThisMonth = this.quotes.filter(invoice => {
          const createdDate = new Date(invoice.quoteDate);
          return (
            invoice.userId === userId &&
            invoice.status === QuoteStatus.SENT &&
            createdDate.getMonth() === currentMonth &&
            createdDate.getFullYear() === currentYear
          );
        }).length;

        if (sentInvoicesThisMonth >= QuotationConstants.FREE_USER_MONTHLY_LIMIT) {

          Swal.fire({
            title: 'Limit Reached!',
            text: QuotationConstants.MESSAGES.FREE_USER_LIMIT_REACHED,
            icon: 'info',
            confirmButtonText: 'Upgrade Plan',
            confirmButtonColor: '#007bff',
          }).then((result) => {
            if (result.isConfirmed) {

              window.location.href = '/manage-subscription';
            }
          });

          return;
        }
      }
  
  const selectedQuotes = this.filteredQuotes.filter(quote => quote.selected);
  if (selectedQuotes.length > 0) {
    const sendQuotes = Array.from(selectedQuotes);
  
    // Filter out Quotes that are not in valid status
    const pendingQuotes = sendQuotes.filter(quote => QuoteStatusUtils.isValidForSending(quote.status));
    const invalidStatusQuotes = sendQuotes.filter(quote =>
      !QuoteStatusUtils.isValidForSending(quote.status)
    );

    if (pendingQuotes.length === 0) {
      Swal.fire({
        title: 'No Valid quotes',
        text: QuotationConstants.MESSAGES.NO_VALID_QUOTES,
        icon: 'warning',
        confirmButtonText: 'OK',
        cancelButtonText: 'Ask Chimp',
        confirmButtonColor: '#be0032',
        cancelButtonColor: '#007bff',
        showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('No Valid quotes');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound() 
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
      }).then(() => {
        this.clearSelectedCheckboxes();
        this.isAllSelected = false;
      });
      return;
    }

     if (!this.TempRecipientEmail) {
            Swal.fire({
              title: 'Missing Email',
              text: QuotationConstants.MESSAGES.MISSING_EMAIL,
              icon: 'error',
              confirmButtonText: 'OK'
            });
            return;
          }

    Swal.fire({
      title: 'Send quotes',
      text: `Do you want to send the selected invoices to ${this.TempRecipientEmail}?`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: 'Send',
      cancelButtonText: 'Cancel',
      confirmButtonColor: '#007bff',
      cancelButtonColor: '#6c757d'
    }).then((result) => {
      if (result.dismiss === Swal.DismissReason.cancel || result.dismiss === Swal.DismissReason.backdrop) {
        this.clearSelectedCheckboxes();
        this.isAllSelected = false;
      }

      if (result.isConfirmed) {

        this.isSending = true;

        // Fetch BusinessPartner details for each invoice
        const businessPartnerFetchPromises = pendingQuotes.map(quote => {
          return this.businessPartnerService.getBusinessPartnerById(quote.businessPartnerId).toPromise();
        });

        // Process the promises to attach emails
        Promise.all(businessPartnerFetchPromises)
          .then(businessPartners => {

            // Attach the email to the corresponding invoice if businessPartner is defined
            businessPartners.forEach((businessPartner, index) => {
              if (businessPartner) {
                pendingQuotes[index].recipient = businessPartner.email;
              } else {
                console.warn('Business partner not found for invoice:', pendingQuotes[index].quoteId);
              }
            });


            const finalContent = this.templateHtmlContent.replace(
              /<body[^>]*>.*<\/body>/is, 
              `<body><p>${this.content.replace(/\n/g, '</p><p>')}</p></body>`
            );
            

          

            // Send the quotes with attached emails
            const emailData = {
              TempRecipientEmail: this.TempRecipientEmail,
              subject: this.subject,  // Input from user for subject
             // content: this.content,  // Input from user for content
             content: finalContent, // Send with HTML tags
             quotes: pendingQuotes
            };

            const entityUuid = localStorage.getItem(QuotationConstants.STORAGE_KEYS.ENTITY_UUID);

          if (!entityUuid) {
            this.isLoading = false;
            alert(QuotationConstants.MESSAGES.MISSING_ENTITY_UUID);
            return;
          }

            this.quotationService.sendQuoteWithEmail(emailData, entityUuid).subscribe(
              (response) => {

                Swal.fire({
                  title: 'Success!',
                  text: QuotationConstants.MESSAGES.SEND_SUCCESS,
                  icon: 'success',
                  confirmButtonText: 'OK',
                  confirmButtonColor: '#28a745',
                }).then(() => {
                  this.isSending = false;
                  this.closeSendQuote.nativeElement.click();
                  this.closePreview.nativeElement.click();
                  this.selectedQuotes.clear();  
                  this.filteredQuotes.forEach(quote => {
                    quote.selected = false;
                  });
                  this.isAllSelected = false;
                   this.fetchQuotations();
                  this.router.navigate(['/quotation']);
                 
                });
              },
              (error) => {
                console.error('Failed to send quotes:', error);
                this.isSending = false;

                Swal.fire({
                  title: 'Error!',
                  text: QuotationConstants.MESSAGES.SEND_ERROR,
                  icon: 'error',
                  confirmButtonText: 'OK',
                  cancelButtonText: 'Ask Chimp',
                  confirmButtonColor: '#be0032',
                  cancelButtonColor: '#007bff',
                  showCancelButton: true,
                  }).then((result) => {
                    if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
                      if (this.chatBotComponent) {
                        Swal.fire({
                          title: 'Processing...',
                          text: 'Please wait while Chimp processes your request.',
                          allowOutsideClick: false,
                          didOpen: () => {
                            Swal.showLoading();
                            this.chatBotComponent.setInputData('Failed to send quotes.');
                            this.chatBotComponent.responseReceived.subscribe(response => {
                              Swal.close();
                              this.chatResponseComponent.showPopup = true;
                              this.chatResponseComponent.responseData = response;
                              this.playLoadingSound();
                              this.stopLoadingSound() 
                            });
                          },
                        });
                      } else {
                        console.error('ChatBotComponent is not available.');
                      }
                    }
                });
              }
            );
          })
          .catch(error => {
            console.error('Failed to fetch business partner details:', error);
            this.isSending = false;
            
            Swal.fire({
              title: 'Error!',
              text: QuotationConstants.MESSAGES.FETCH_BP_ERROR,
              icon: 'error',
              confirmButtonText: 'OK',
              cancelButtonText: 'Ask Chimp',
        confirmButtonColor: '#be0032',
        cancelButtonColor: '#007bff',
        showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Failed to fetch business partner details.');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound() 
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
            });
          });
      }
    });
  } else {
    Swal.fire({
      title: 'No quotes Selected',
      text: 'Please select quotes to send.',
      icon: 'warning',
      confirmButtonText: 'OK',
      cancelButtonText: 'Ask Chimp',
        confirmButtonColor: '#be0032',
        cancelButtonColor: '#007bff',
        showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('No quotes Selected');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound() 
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
    });
  }
}

ngOnDestroy() {
  if (this.modalHideSubscription) {
    this.modalHideSubscription.unsubscribe();
  }
}

clearSelectedCheckboxes() {
  // Deselect all checkboxes
  this.selectedQuotes.clear();
}
  private getQuoteHeadList() {

    this.quotationService.getQuoteHeadList().subscribe(data => {
      this.quoteHeadList = data;
      this.filterQuotes(); // Filter quotes initially
    });
  }

  toggleSelection(quotation: QuoteHead, event: any): void {
    if (event.target.checked) {
      this.selectedQuotes.add(quotation);
    } else {
      this.selectedQuotes.delete(quotation);
    }
  }
  fetchQuotations() {

    const entityId = +((localStorage.getItem(QuotationConstants.STORAGE_KEYS.ENTITY_ID)) + "");

    this.quotationService.getAllSalesQuotesHeadList(entityId).subscribe(
      (data: QuoteHead[]) => {
        this.quotes = data;
        this.filteredQuotes = this.quotes;
      },
      (error) => {

      }
    );
  }

  onSearchChange() {
    this.filterQuotes(); // Call filter function on input change
  }

  setActiveTab(tab: string) {
    this.activeTab = tab;
    if (this.searchTerm || this.startDate || this.endDate) {
      this.filterQuotes(); // Filter quotes only if search criteria are provided
    } else {
      this.filteredQuotes = this.activeTab === 'all' 
        ? this.quotes 
        : this.quotes.filter(quote => quote.status.toLowerCase() === this.activeTab);
    }
  }

  filterQuotes(): void {
    if (!this.searchTerm && !this.startDate && !this.endDate) {
      Swal.fire({
        icon: 'warning',
        title: 'Missing Search Criteria',
        text: QuotationConstants.MESSAGES.SEARCH_CRITERIA_MISSING,
        confirmButtonText: 'OK',
        confirmButtonColor: '#007bff',
      });
      return;
    }

    const searchTermLower = this.searchTerm.toLowerCase().trim();
    let filtered = this.quotes;

    // Filter by active tab
    if (this.activeTab !== 'all') {
      filtered = filtered.filter(quote => quote.status.toLowerCase() === this.activeTab);
    }

    // Filter by search term
    if (searchTermLower) {
      filtered = filtered.filter(quote =>
        quote.quoteNumber.toString().toLowerCase().includes(searchTermLower) ||
        quote.customerName.toLowerCase().includes(searchTermLower)
      );
    }
  
    // Filter by date range
    if (this.startDate) {
      const startDate = new Date(this.startDate);
      filtered = filtered.filter(quote => new Date(quote.quoteDate) >= startDate);
    }
  
    if (this.endDate) {
      const endDate = new Date(this.endDate);
      filtered = filtered.filter(quote => new Date(quote.quoteDate) <= endDate);
    }
  
    this.filteredQuotes = filtered;
  }

  // Reset filters
  resetFilters() {
    this.searchTerm = '';
    this.startDate = null;
    this.endDate = null;
    this.activeTab = 'all'; // Reset the active tab to 'all'
    this.filteredQuotes = this.quotes;
  }


  deleteQuotation(id: number, index: number) {
    Swal.fire({
      title: 'Delete Quotation',
      text: QuotationConstants.MESSAGES.DELETE_CONFIRMATION,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, cancel!',
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
    }).then((result) => {
      if (result.isConfirmed) {
        this.quotationService.deleteSalesQuotesHead(id).subscribe(
          () => {
            Swal.fire({
              title: 'Deleted!',
              text: QuotationConstants.MESSAGES.DELETE_SUCCESS,
              icon: 'success',
              confirmButtonText: 'OK',
              confirmButtonColor: '#3085d6',
            });
            this.quotes.splice(index, 1); // Remove the quotation from the array
          },
          (error) => {
            console.error('Error deleting quotation:', error);
            Swal.fire({
              title: 'Error!',
              text: QuotationConstants.MESSAGES.DELETE_ERROR,
              icon: 'error',
              confirmButtonText: 'OK',
              cancelButtonText: 'Ask Chimp',
              confirmButtonColor: '#be0032',
              cancelButtonColor: '#007bff',
              showCancelButton: true,
            }).then((result) => {
              if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
                if (this.chatBotComponent) {
                  Swal.fire({
                    title: 'Processing...',
                    text: 'Please wait while Chimp processes your request.',
                    allowOutsideClick: false,
                    didOpen: () => {
                      Swal.showLoading();
                      this.chatBotComponent.setInputData('Failed to delete quotation.');
                      this.chatBotComponent.responseReceived.subscribe(response => {
                        Swal.close();
                        this.chatResponseComponent.showPopup = true;
                        this.chatResponseComponent.responseData = response;
                        this.playLoadingSound();
                        this.stopLoadingSound() 
                      });
                    },
                  });
                } else {
                  console.error('ChatBotComponent is not available.');
                }
              }
            });
          }
        );
      } else if (result.dismiss === Swal.DismissReason.cancel) {
        Swal.fire({
          title: 'Cancelled',
          text: QuotationConstants.MESSAGES.DELETE_CANCELLED,
          icon: 'info',
          confirmButtonText: 'OK',
          confirmButtonColor: '#3085d6',
        });
      }
    });
  }

  editQuotation(id: number) {
    this.router.navigate(['/edit-quotation', id]);
  }

  viewQuotation(id: number) {
    this.router.navigate(['/view-quotation', id]);
  }

  createInvoice(id: number) {
   /** const userString = localStorage.getItem('user');
    if (!userString) {
      // Handle case where user is not found
      return;
    }
    
    const currentUser = JSON.parse(userString); // Now it's an object
    
    const userRole = currentUser.roleName;
    const userId = currentUser.id;
    
    if (userRole === 'Free') {
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
  
      // Count user's invoice created this month
      const freeUserInvoicesCount = this.invoices.filter(invoice => {
        const createdDate = new Date(invoice.postingDate);
        return invoice.userId === userId &&
               createdDate.getMonth() === currentMonth &&
               createdDate.getFullYear() === currentYear;
      }).length;
  
      if (freeUserInvoicesCount >= 5) {
        Swal.fire({
          title: 'Limit Reached!',
          text: 'As a Free user, you can only create up to 5 Invoices per month.',
          icon: 'info',
          confirmButtonText: 'Upgrade Plan',
          confirmButtonColor: '#007bff',
        });
        return;
      }
    }**/
  
    this.router.navigate(['/copy-from-quotation', id]);
  }

  createQuote() {
    
   /** const userString = localStorage.getItem('user');
    if (!userString) {
      // Handle case where user is not found
      return;
    }
    
    const currentUser = JSON.parse(userString); // Now it's an object
    
    const userRole = currentUser.roleName;
    const userId = currentUser.id;
    
    if (userRole === 'Free') {
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
  
      // Count user's quotes created this month
      const freeUserQuotesCount = this.quotes.filter(quote => {
        const createdDate = new Date(quote.quoteDate);
        return quote.userId === userId &&
               createdDate.getMonth() === currentMonth &&
               createdDate.getFullYear() === currentYear;
      }).length;
  
      if (freeUserQuotesCount >= 5) {
        Swal.fire({
          title: 'Limit Reached!',
          text: 'As a Free user, you can only create up to 5 quotes per month.',
          icon: 'info',
          confirmButtonText: 'Upgrade Plan',
          confirmButtonColor: '#007bff',
        });
        return;
      }
    }**/
    this.router.navigate(['/create-quotation']);
  }

    handleReviseQuotation() {
      const selectedQuotes = this.quotes.filter(quote => quote.selected && quote.status === QuoteStatus.SENT);
      if (selectedQuotes.length > 1) {
        Swal.fire({
          title: 'Warning!',
          text: QuotationConstants.MESSAGES.MULTIPLE_QUOTES_REVISION,
          icon: 'warning',
          confirmButtonText: 'OK',
          confirmButtonColor: '#007bff',
        });
        return;
      }
      if (selectedQuotes.length === 0) {
        Swal.fire({
          title: 'Warning!',
          text: QuotationConstants.MESSAGES.NO_SENT_QUOTE_SELECTED,
          icon: 'warning',
          confirmButtonText: 'OK',
          confirmButtonColor: '#007bff',
        });
        return;
      }
      const quoteId = selectedQuotes[0].quoteId; // Assuming you revise only one quote at a time
      this.reviseQuotation(quoteId);
    }
  reviseQuotation(id: number) {
    this.router.navigate(['/revise-quote', id]);
  }

  selectAll(event: any) {
    const isChecked = event.target.checked;
    this.quotes.forEach(quote => quote.selected = isChecked);
  }

  archive() {

  }
  markas() {

  }
    handleCopyFromQuote() {
      
      const selectedQuotes = this.quotes.filter(quote => quote.selected);
    
      if (selectedQuotes.length === 0) {
        Swal.fire({
          title: 'Error!',
          text: QuotationConstants.MESSAGES.NO_QUOTE_TO_COPY,
          icon: 'warning',
          showCancelButton: true,
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Please select a Quote to Copy.');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound() 
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
        return;
      }
    
      if (selectedQuotes.length > 1) {
        Swal.fire({
          title: 'Warning!',
          text: QuotationConstants.MESSAGES.MULTIPLE_QUOTES_COPY,
          icon: 'warning',
          confirmButtonText: 'OK',
          confirmButtonColor: '#007bff',
        });
        return;
      }
    
      const quoteId = selectedQuotes[0].quoteId; // Assuming you only revise one quote at a time
      this.copyQuotation(quoteId);
    }
    

  copyQuotation(id: number) {
    this.router.navigate(['/copy-quote', id]);
  }
  @ViewChild('quotationPreviewFrame') quotationPreviewFrame!: ElementRef;
  isLoading = false;
  private quotationCache = new Map<number, string>();


  handlePreviewQuotation() {
    // Filter selected quotes with 'Pending' status
    const selectedQuotes = this.quotes.filter(quote => quote.selected);


    // Ensure only one quote is selected for preview
  if (selectedQuotes.length === 0) {
    Swal.fire({
      title: 'Warning!',
      text: QuotationConstants.MESSAGES.NO_QUOTE_TO_PREVIEW,
      icon: 'warning',
      confirmButtonText: 'OK',
      confirmButtonColor: '#007bff',
    });
    return;
  }

  if (selectedQuotes.length > 1) {
    Swal.fire({
      title: 'Warning!',
      text: QuotationConstants.MESSAGES.MULTIPLE_QUOTES_PREVIEW,
      icon: 'warning',
      confirmButtonText: 'OK',
      confirmButtonColor: '#007bff',
    });
    return;
  }
    const quoteId = selectedQuotes[0].quoteId; // Assuming you revise only one quote at a time
    this.previewQuotation(quoteId);
  }

  previewQuotation(quoteId: number) {
    this.isLoading = true;

    const quote = this.filteredQuotes.find(inv => inv.quoteId === quoteId);

    if (quote) {
      const fakeEvent = { target: { checked: true } };
      this.toggleSelection(quote, fakeEvent);
    }

    const cachedBase64String = this.quotationCache.get(quoteId);
    if (cachedBase64String) {
      this.loadPdfIntoIframe(cachedBase64String);
      return;
    }
    const entityId = +((localStorage.getItem(QuotationConstants.STORAGE_KEYS.ENTITY_ID)) + "");

    const entityUuid = localStorage.getItem(QuotationConstants.STORAGE_KEYS.ENTITY_UUID);

          if (!entityUuid) {
            this.isLoading = false;
            alert(QuotationConstants.MESSAGES.MISSING_ENTITY_UUID);
            return;
          }

    this.quotationService.getQuoteReport(quoteId,entityId, entityUuid).subscribe(
      data => {
        const base64String = data.response;

        if (base64String) {
          this.quotationCache.set(quoteId, base64String);
          this.loadPdfIntoIframe(base64String);
        } else {
          this.isLoading = false;
          alert(QuotationConstants.MESSAGES.NO_QUOTATION_DATA);
        }
      },
      error => {
        this.isLoading = false;
        alert(QuotationConstants.MESSAGES.QUOTATION_PREVIEW_ERROR);
      }
    );
  }
  private loadPdfIntoIframe(base64String: string) {
    const pdfData = 'data:application/pdf;base64,' + base64String;
    const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(pdfData) as any;
    const iframe = this.quotationPreviewFrame.nativeElement;
    iframe.onload = () => {
      this.isLoading = false;
    };
    iframe.setAttribute('src', sanitizedUrl.changingThisBreaksApplicationSecurity);
  }
  
  handleCreateInvoice() {
   // Filter selected quotes with valid status for invoice creation
    const selectedQuotes = this.quotes.filter(quote =>
      quote.selected && QuoteStatusUtils.isValidForInvoiceCreation(quote.status)
    );

    // Validate if more than one quote is selected
    if (selectedQuotes.length > 1) {
      Swal.fire({
        title: 'Warning!',
        text: QuotationConstants.MESSAGES.MULTIPLE_QUOTES_INVOICE,
        icon: 'warning',
        confirmButtonText: 'OK',
        confirmButtonColor: '#007bff',
      });
      return;
    }

    // Validate if no valid quote is selected
    if (selectedQuotes.length === 0) {
      Swal.fire({
        title: 'Warning!',
        text: QuotationConstants.MESSAGES.NO_PENDING_QUOTE_FOR_INVOICE,
        icon: 'warning',
        confirmButtonText: 'OK',
        confirmButtonColor: '#007bff',
      });
      return;
    }
  
    // Proceed to create the invoice for the selected quote
    // Add your invoice creation logic here
   
    const quoteId = selectedQuotes[0].quoteId; // Assuming you revise only one quote at a time
    this.createInvoice(quoteId);
  }

  playLoadingSound() {
    let audio = new Audio();
    audio.src = QuotationConstants.AUDIO_PATH;
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0; 
    }
  }
  exportToExcel(): void {
    if (!this.filteredQuotes || this.filteredQuotes.length === 0) {
      alert(QuotationConstants.MESSAGES.NO_DATA_TO_EXPORT);
      return;
    }

    const exportData = this.filteredQuotes.map(quote => ({
      'Quote ID': quote.quoteId,
      'Quote Number': quote.quoteNumber,
      'Customer Name': quote.customerName,
      'Quote Date': new Date(quote.quoteDate).toISOString().split('T')[0], // Format date as YYYY-MM-DD
      'Valid Until': new Date(quote.validUntilDate).toISOString().split('T')[0], // Format date as YYYY-MM-DD
      'Amount': quote.grandTotal,
      'Status': quote.status
    }));

    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, QuotationConstants.EXCEL_EXPORT.SHEET_NAME);

    const excelBuffer: any = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });

    const timestamp = new Date().toISOString().replace(/[:.-]/g, '_');
    this.saveAsExcelFile(excelBuffer, `${QuotationConstants.EXCEL_EXPORT.FILE_PREFIX}${timestamp}`);
}

private saveAsExcelFile(buffer: any, fileName: string): void {
    const data: Blob = new Blob([buffer], {
      type: QuotationConstants.EXCEL_EXPORT.MIME_TYPE
    });

    saveAs(data, `${fileName}.xlsx`);
}
}
