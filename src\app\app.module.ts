import { NgModule, isDevMode } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { HttpClientModule, HttpClientJsonpModule,HTTP_INTERCEPTORS,  } from '@angular/common/http';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { MatSidenavModule } from '@angular/material/sidenav';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatTableModule } from '@angular/material/table';
import { MatSortModule } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { CommonModule, DatePipe } from '@angular/common';
import { NgxPaginationModule } from 'ngx-pagination';
import { MatDialogModule } from '@angular/material/dialog';
import { MatSelectModule } from '@angular/material/select';
import { MatPaginatorModule } from '@angular/material/paginator';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { HomeComponent } from './home/<USER>';
import { CreateUserComponent } from './modules/admin/components/user/create-user/create-user.component';
import { UserLoginComponent } from './modules/admin/components/user/user-login/user-login.component';
import { HeaderComponent } from './modules/admin/components/header/header.component';
import { NavbarComponent } from './modules/admin/components/navbar/navbar.component';
import { SalesDashboardComponent } from './modules/admin/components/dashboard/sales-dashboard/sales-dashboard.component';
import { CreateEntityComponent } from './modules/entity/create-entity/create-entity.component';
import { PaymentComponent } from './modules/subscription/payment/payment.component';
import { ContactUsComponent } from './pages/contact-us/contact-us.component';
import { QuotationComponent } from './modules/quotation/quotation.component';
import { CreateQuotationComponent } from './modules/quotation/create-quotation/create-quotation.component';
import { CreditNoteComponent } from './modules/credit-note/credit-note-list/credit-note.component';
import { CreateCreditNoteComponent } from './modules/credit-note/create-credit-note/create-credit-note.component';
import { CreateBusinessPartnerComponent } from './modules/business-partner/create-business-partner/create-business-partner.component';
import { CreateInvoiceComponent } from './modules/invoice/create-invoice/create-invoice.component';
import { InvoiceComponent } from './modules/invoice/invoice-list/invoice.component';
import { EditQuotationComponent } from './modules/quotation/edit-quotation/edit-quotation.component';
import { UpdateInvoiceComponent } from './modules/invoice/update-invoice/update-invoice.component';
import { HomeHeaderComponent } from './home/<USER>/home-header.component';
import { HomeFooterComponent } from './home/<USER>/home-footer.component';
import { UserAgreementComponent } from './modules/admin/components/user/user-agreement/user-agreement.component';
import { ReviseInvoiceComponent } from './modules/invoice/revise-invoice/revise-invoice.component';
import { HomeFeaturesComponent } from './home/<USER>/home-features.component';
import { ReviseQuotationComponent } from './modules/quotation/revise-quotation/revise-quotation.component';
import { ManageSubscriptionComponent } from './modules/subscription/manage-subscription/manage-subscription.component';
import { UserInvitationComponent } from './modules/admin/components/user/user-invitation/user-invitation.component';
import { UserRegistrationComponent } from './modules/admin/components/user/user-registration/user-registration.component';
import { CopyFromQuotationComponent } from './modules/invoice/copy-from-quotation/copy-from-quotation.component';
import { QuotationReportComponent } from './modules/quotation/quotation-report/quotation-report.component';
import { InvoiceReportComponent } from './modules/invoice/invoice-report/invoice-report.component';
import { CreditNoteReportComponent } from './modules/credit-note/credit-note-report/credit-note-report.component';
import { PaymentReceiptComponent } from './modules/invoice/payment-receipt/payment-receipt.component';
import { CreatePaymentReceiptComponent } from './modules/invoice/create-payment-receipt/create-payment-receipt.component';
import { CopyFromQuoteComponent } from './modules/quotation/copy-from-quote/copy-from-quote.component';
import { CreateUserThroughInvitationComponent } from './modules/admin/components/user/create-user-through-invitation/create-user-through-invitation.component';
import { MatNativeDateModule, MatOptionModule } from '@angular/material/core';
import { BusinessEntityEditComponent } from './modules/settings/business-entity-edit/business-entity-edit.component';
import { EntitySelectionComponent } from './modules/entity/entity-selection/entity-selection.component';
import { CustomerStatementComponent } from './modules/customer-statement/customer-statement.component';
import { HelpPageComponent } from './pages/help/help-page/help-page.component';
import { ForgotPasswordComponent } from './modules/admin/components/user/user-login/forgot-password/forgot-password.component';
import { ResetPasswordComponent } from './modules/admin/components/user/user-login/reset-password/reset-password.component';
import { AccountStatementComponent } from './modules/account-statement/account-statement.component';
import { PaymentReceiptReportComponent } from './modules/invoice/payment-receipt-report/payment-receipt-report.component';
import { CreateCreditNoteUserComponent } from './modules/credit-note/create-credit-note-user/create-credit-note-user.component';
import { CreatePaymentReceiptUserComponent } from './modules/invoice/create-payment-receipt-user/create-payment-receipt-user.component';
import { CreateCountryComponent } from './modules/entity/country/create-country/create-country.component';
import { ListCountryComponent } from './modules/entity/country/list-country/list-country.component';
import { UpdateCountryComponent } from './modules/entity/country/update-country/update-country.component';
import { ListBusinessPartnerComponent } from './modules/business-partner/list-business-partner/list-business-partner.component';
import { UpdateBusinessPartnerComponent } from './modules/business-partner/update-business-partner/update-business-partner.component';
import { UserProfileComponent } from './modules/admin/components/user/user-profile/user-profile.component';
import { CreateItemComponent } from './modules/entity/item/create-item/create-item.component';
import { ListItemComponent } from './modules/entity/item/list-item/list-item.component';
import { UpdateItemComponent } from './modules/entity/item/update-item/update-item.component';
import { ChatAssistantComponent } from './chat-bot/chat-assistant/chat-assistant.component';
import { BotControllerComponent } from './chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from './chat-bot/chat-response/chat-response.component';
import { SidebarComponent } from './modules/admin/components/sidebar/sidebar.component';
import { AdminNavbarComponent } from './modules/admin/components/navbar/admin-navbar/admin-navbar.component';
import { AdminNavigationComponent } from './modules/admin/components/admin-navigation/admin-navigation.component';
import { SalesNavigationComponent } from './modules/admin/components/sales-navigation/sales-navigation.component';
import { CreateGlAccountComponent } from './modules/finance-module/gl-account/create-gl-account/create-gl-account.component';
import { UpdateGlAccountComponent } from './modules/finance-module/gl-account/update-gl-account/update-gl-account.component';
import { GlAccountListComponent } from './modules/finance-module/gl-account/gl-account-list/gl-account-list.component';
import { TransactionListComponent } from './modules/finance-module/transaction/transaction-list/transaction-list.component';
import { UpdateTransactionComponent } from './modules/finance-module/transaction/update-transaction/update-transaction.component';
import { CreateTransactionComponent } from './modules/finance-module/transaction/create-transaction/create-transaction.component';
import { PayrollSettingsComponent } from './modules/payroll-module/payroll-settings/payroll-settings.component';
import { UpdatePayCalendarComponent } from './modules/payroll-module/pay-calendar/update-pay-calendar/update-pay-calendar.component';
import { BASComponent } from './modules/finance-module/bas/bas.component';
import { InviteAccountantComponent } from './modules/accountant/invite-accountant/invite-accountant.component';
import { AddAccountantComponent } from './modules/accountant/add-accountant/add-accountant.component';
import { RequestEntityComponent } from './modules/accountant/request-entity/request-entity.component';
import { AcceptRequestComponent } from './modules/accountant/accept-request/accept-request.component';
import { EntityRequestsComponent } from './modules/entity/entity-requests/entity-requests.component';
import { EmpolyeeComponent } from './modules/payroll-module/payroll-settings/empolyee/empolyee.component';
import { SubscriptionPlanListComponent } from './modules/subscription/subscription-plan-list/subscription-plan-list.component';
import { InviteEntityComponent } from './modules/entity/invite-entity/invite-entity.component';
import { UpdateEarningComponent } from './modules/payroll-module/update-earning/update-earning.component';
import { PayableBillListComponent } from './modules/finance-module/bill/payable-bill-list/payable-bill-list.component';
import { AddPayableBillComponent } from './modules/finance-module/bill/add-payable-bill/add-payable-bill.component';
import { UpdateDeductionComponent } from './modules/payroll-module/update-deduction/update-deduction.component';
import { PayRunUsersComponent } from './modules/payroll-module/payroll-settings/pay-run-users/pay-run-users.component';
import { BankAccountComponent } from './modules/finance-module/bank/bank-account/bank-account.component';
import { BankAccountsComponent } from './modules/finance-module/bank/bank-accounts/bank-accounts.component';
import { CreateJvComponent } from './modules/finance-module/journal-voucher/create-jv/create-jv.component';
import { JvListComponent } from './modules/finance-module/journal-voucher/jv-list/jv-list.component';
import { PayrollNevigationComponent } from './modules/admin/components/payroll-nevigation/payroll-nevigation.component';
import { PayrollCalendarComponent } from './modules/payroll-module/payroll-calendar/payroll-calendar.component';
import { PayrollPayItemsComponent } from './modules/payroll-module/payroll-pay-items/payroll-pay-items.component';
import { PayrollPayRunComponent } from './modules/payroll-module/payroll-pay-run/payroll-pay-run.component';
import { ExpenceClaimsComponent } from './modules/finance-module/expence-claims/expence-claims.component';
import { PayRunUserDetailsComponent } from './modules/payroll-module/payroll-pay-run/pay-run-user-details/pay-run-user-details.component';
import { RecordBatchPaymentsComponent } from './modules/finance-module/bill/record-batch-payments/record-batch-payments.component';
import { FinanceNavbarComponent } from './modules/admin/components/navbar/finance-navbar/finance-navbar.component';
import { PayRunPayTemplateComponent } from './modules/payroll-module/payroll-pay-run/pay-run-pay-template/pay-run-pay-template.component';
import { FinanceDashboardComponent } from './modules/admin/components/dashboard/finance-dashboard/finance-dashboard.component';
import { ExpenceClaimsListComponent } from './modules/finance-module/expence-claims/expence-claims-list/expence-claims-list.component';
import { ReccordBatchPaymentsListComponent } from './modules/finance-module/bill/reccord-batch-payments-list/reccord-batch-payments-list.component';
import { EditJvComponent } from './modules/finance-module/journal-voucher/edit-jv/edit-jv.component';
import { PaymentExpensesComponent } from './modules/finance-module/expence-claims/payment-expenses/payment-expenses.component';
import { PaymentExpensesListComponent } from './modules/finance-module/expence-claims/payment-expenses-list/payment-expenses-list.component';
import { ReportsComponent } from './modules/payroll-module/payroll-settings/reports/reports.component';
import { CreditNoteBillComponent } from './modules/finance-module/bill/credit-note-bill/credit-note-bill.component';
import { CreditNoteBillListComponent } from './modules/finance-module/bill/credit-note-bill-list/credit-note-bill-list.component';
import { PayrollPayrollSettingsComponent } from './modules/payroll-module/payroll-pay-run/payroll-payroll-settings/payroll-payroll-settings.component';
import { ReportPageComponent } from './modules/payroll-module/payroll-settings/report-page/report-page.component';
import { PayableBillListReportComponent } from './modules/finance-module/bill/payable-bill-list-report/payable-bill-list-report.component';
import { JvListReportComponent } from './modules/finance-module/journal-voucher/jv-list-report/jv-list-report.component';
import { EditPayableBillComponent } from './modules/finance-module/bill/edit-payable-bill/edit-payable-bill.component';
import { CreditNoteBillListReportComponent } from './modules/finance-module/bill/credit-note-bill-list-report/credit-note-bill-list-report.component';
import { RecordBatchPaymentsListReportComponent } from './modules/finance-module/bill/record-batch-payments-list-report/record-batch-payments-list-report.component';
import { GlPostingListComponent } from './modules/finance-module/gl-posting/gl-posting-list/gl-posting-list.component';
import { GlPostingViewPopUpComponent } from './modules/finance-module/gl-posting/gl-posting-view-pop-up/gl-posting-view-pop-up.component';
import { GlReportComponent } from './modules/finance-module/gl-posting/gl-report/gl-report.component';
import { GlViewEachAccountComponent } from './modules/finance-module/gl-posting/gl-view-each-account/gl-view-each-account.component';
import { PeriodClosingComponent } from './modules/finance-module/period-closing/period-closing/period-closing.component';
import { PnlReportComponent } from './modules/finance-module/p&l/pnl-report/pnl-report.component';
import { BsReportComponent } from './modules/finance-module/balance-sheet/bs-report/bs-report.component';
import { SupplierStatementComponent } from './modules/finance-module/supplier-statement/supplier-statement.component';
import { PaySlipComponent } from './modules/payroll-module/payroll-settings/reports/pay-slip/pay-slip/pay-slip.component';
import { EditExpenceComponent } from './modules/finance-module/expence-claims/edit-expence/edit-expence.component';
import { GlPostingListBySuppliersComponent } from './modules/finance-module/gl-posting/gl-posting-list-by-suppliers/gl-posting-list-by-suppliers.component';
import { ImportBankReconciliationComponent } from './modules/finance-module/bank-reconciliation/import-bank-reconciliation/import-bank-reconciliation.component';
import { BankAccountListComponent } from './modules/finance-module/bank-reconciliation/bank-account-list/bank-account-list.component';
import { UserVerificationComponent } from './modules/admin/components/user/user-verification/user-verification.component';
import { SuperannuationComponent } from './modules/payroll-module/superannuation/superannuation/superannuation.component';
import { MatchTransactionsComponent } from './modules/finance-module/bank-reconciliation/match-transactions/match-transactions/match-transactions.component';
import { ViewQuotationComponent } from './modules/quotation/view-quotation/view-quotation.component';
import { InvoiceCashReportComponent } from './modules/invoice/invoice-cash-report/invoice-cash-report.component';
import { ViewInvoiceComponent } from './modules/invoice/view-invoice/view-invoice.component';
import { PaymentSuccessComponent } from './pages/stripe/payment-success/payment-success.component';
import { SecurityHeadersInterceptor } from './interceptors/security-headers.interceptor';
import { WhyChooseUsComponent } from './pages/why-choose-us/why-choose-us.component';
import { BookkeeperOrAccountantComponent } from './pages/bookkeeper-or-accountant/bookkeeper-or-accountant.component';
import { PayrollComponent } from './pages/payroll/payroll.component';
import { InvoicingAndQuotingComponent } from './pages/invoicing-and-quoting/invoicing-and-quoting.component';
import { CustomHeaderComponent } from './home/<USER>/custom-header.component';
import { ExpenseTrackingComponent } from './pages/expense-tracking/expense-tracking.component';
import { GstBasComponent } from './pages/gst-bas/gst-bas.component';
import { LogoHeaderComponent } from './home/<USER>/logo-header.component';
import { ReportingComponent } from './pages/reporting/reporting.component';
import { PartnershipComponent } from './pages/partnership/partnership.component';
import { ManageUsersComponent } from './modules/admin/components/user/manage-users/manage-users.component';
import { RecaptchaModule } from 'ng-recaptcha';
import { DownloadButtonsComponent } from './home/<USER>/download-buttons.component';
import { ServiceWorkerModule } from '@angular/service-worker';
import { ViewPayableBillComponent } from './modules/finance-module/bill/view-payable-bill/view-payable-bill.component';
import { BasPeriodComponent } from './modules/finance-module/bas/bas-period/bas-period/bas-period.component';
import { SalesReportsComponent } from './modules/quotation/sales-reports/sales-reports/sales-reports.component';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { BasViewComponent } from './modules/finance-module/bas/bas-view/bas-view.component';
import { FinanceReportsComponent } from './modules/finance-module/finance-reports/finance-reports.component';
import { ReplaceUnderscoreWithSpacePipe } from './pipes/replace-underscore-with-space.pipe';
import { BankRuleListComponent } from './modules/finance-module/bank-reconciliation/bank-rule-list/bank-rule-list.component';
import {MatTabsModule} from '@angular/material/tabs';
import { CreateBankRuleComponent } from './modules/finance-module/bank-reconciliation/create-bank-rule/create-bank-rule.component';
import { StpComponent } from './modules/payroll-module/stp/stp.component';
import { GlViewPnlComponent } from './modules/finance-module/gl-posting/gl-view-pnl/gl-view-pnl.component';
import { FinalBankRecReportComponent } from './modules/finance-module/bank-reconciliation/final-bank-rec-report/final-bank-rec-report.component';
import { AddItemPopupComponent } from './modules/entity/item/add-item-popup/add-item-popup/add-item-popup.component';
import { AddBusinessPartnerPopupComponent } from './modules/business-partner/add-business-partner-popup/add-business-partner-popup.component';

@NgModule({
  declarations: [
    AppComponent,
    HomeComponent,
    CreateUserComponent,
    UserInvitationComponent,
    UserLoginComponent,
    UserAgreementComponent,
    HeaderComponent,
    NavbarComponent,
    SalesDashboardComponent,
    CreateEntityComponent,
    PaymentComponent,
    ContactUsComponent,
    QuotationComponent,
    CreateQuotationComponent,
    CreditNoteComponent,
    CreateCreditNoteComponent,
    CreateBusinessPartnerComponent,
    InvoiceComponent,
    CreateInvoiceComponent,
    EditQuotationComponent,
    UpdateInvoiceComponent,
    HomeHeaderComponent,
    HomeFooterComponent,
    ReviseInvoiceComponent,
    HomeFeaturesComponent,
    ReviseQuotationComponent,
    ManageSubscriptionComponent,
    UserRegistrationComponent,
    CopyFromQuotationComponent,
    QuotationReportComponent,
    InvoiceReportComponent,
    CreditNoteReportComponent,
    PaymentReceiptComponent,
    CreatePaymentReceiptComponent,
    CopyFromQuoteComponent,
    CreateUserThroughInvitationComponent,
    BusinessEntityEditComponent,
    EntitySelectionComponent,
    CustomerStatementComponent,
    HelpPageComponent,
    ForgotPasswordComponent,
    ResetPasswordComponent,
    AccountStatementComponent,
    PaymentReceiptReportComponent,
    CreateCreditNoteUserComponent,
    CreatePaymentReceiptUserComponent,
    CreateCountryComponent,
    ListCountryComponent,
    UpdateCountryComponent,
    ListBusinessPartnerComponent,
    UpdateBusinessPartnerComponent,
    UserProfileComponent,
    CreateItemComponent,
    ListItemComponent,
    UpdateItemComponent,
    ChatAssistantComponent,
    BotControllerComponent,
    ChatResponseComponent,
    SidebarComponent,
    AdminNavbarComponent,
    AdminNavigationComponent,
    SalesNavigationComponent,
    CreateGlAccountComponent,
    UpdateGlAccountComponent,
    GlAccountListComponent,
    TransactionListComponent,
    UpdateTransactionComponent,
    CreateTransactionComponent,
    PayrollSettingsComponent,
    UpdatePayCalendarComponent,
    BASComponent,
    InviteAccountantComponent,
    AddAccountantComponent,
    RequestEntityComponent,
    AcceptRequestComponent,
    EntityRequestsComponent,
    EmpolyeeComponent,
    SubscriptionPlanListComponent,
    InviteEntityComponent,
    UpdateEarningComponent,
    PayableBillListComponent,
    AddPayableBillComponent,
    UpdateDeductionComponent,
    PayRunUsersComponent,
    BankAccountComponent,
    BankAccountsComponent,
    CreateJvComponent,
    JvListComponent,
    PayrollNevigationComponent,
    PayrollCalendarComponent,
    PayrollPayItemsComponent,
    PayrollPayRunComponent,
    ExpenceClaimsComponent,
    PayRunUserDetailsComponent,
    RecordBatchPaymentsComponent,
    FinanceNavbarComponent,
    PayRunPayTemplateComponent,
    FinanceDashboardComponent,
    ExpenceClaimsListComponent,
    ReccordBatchPaymentsListComponent,
    EditJvComponent,
    PaymentExpensesComponent,
    PaymentExpensesListComponent,
    ReportsComponent,
    CreditNoteBillComponent,
    CreditNoteBillListComponent,
    PayrollPayrollSettingsComponent,
    ReportPageComponent,
    PayableBillListReportComponent,
    JvListReportComponent,
    EditPayableBillComponent,
    CreditNoteBillListReportComponent,
    RecordBatchPaymentsListReportComponent,
    GlPostingListComponent,
    GlPostingViewPopUpComponent,
    GlReportComponent,
    GlViewEachAccountComponent,
    PeriodClosingComponent,
    PnlReportComponent,
    BsReportComponent,
    SupplierStatementComponent,
    PaySlipComponent,
    EditExpenceComponent,
    GlPostingListBySuppliersComponent,
    ImportBankReconciliationComponent,
    BankAccountListComponent,
    UserVerificationComponent,
    SuperannuationComponent,
    MatchTransactionsComponent,
    ViewQuotationComponent,
    InvoiceCashReportComponent,
    ViewInvoiceComponent,
    PaymentSuccessComponent,
    WhyChooseUsComponent,
    BookkeeperOrAccountantComponent,
    PayrollComponent,
    InvoicingAndQuotingComponent,
    CustomHeaderComponent,
    ExpenseTrackingComponent,
    GstBasComponent,
    LogoHeaderComponent,
    ReportingComponent,
    PartnershipComponent,
    ManageUsersComponent,
    DownloadButtonsComponent,
    ViewPayableBillComponent,
    BasPeriodComponent,
    SalesReportsComponent,
    BasViewComponent,
    ReplaceUnderscoreWithSpacePipe,
    BankRuleListComponent,
    CreateBankRuleComponent,
    HomeHeaderComponent,
    StpComponent,
    FinanceReportsComponent,
    HomeHeaderComponent,
    GlViewPnlComponent,
    FinalBankRecReportComponent,
    AddItemPopupComponent,
    AddBusinessPartnerPopupComponent

  ],
  imports: [
    CommonModule,
    BrowserModule,
    AppRoutingModule,
    FormsModule,
    HttpClientModule,
    HttpClientJsonpModule,
    ReactiveFormsModule,
    BrowserAnimationsModule,
    MatSidenavModule,
    NgSelectModule,
    NgxMatSelectSearchModule,
    MatSnackBarModule,
    MatFormFieldModule,
    MatDatepickerModule,
    MatTableModule,
    MatSortModule,
    MatInputModule,
    MatPaginatorModule,
    MatOptionModule,
    MatSelectModule,
    MatNativeDateModule,
    FormsModule,
    NgxPaginationModule,
    MatDialogModule,
    RecaptchaModule,
    MatAutocompleteModule,
    MatTabsModule,
    ServiceWorkerModule.register('ngsw-worker.js', {
      enabled: !isDevMode(),
      // Register the ServiceWorker as soon as the application is stable
      // or after 30 seconds (whichever comes first).
      registrationStrategy: 'registerWhenStable:30000'
    }),
  ],
  providers: [
    DatePipe,
    { provide: HTTP_INTERCEPTORS, useClass: SecurityHeadersInterceptor, multi: true }
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
