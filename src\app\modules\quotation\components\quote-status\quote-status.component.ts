import { Component, Input } from '@angular/core';
import { QuoteStatusUtils } from '../../quotation.constants';

@Component({
  selector: 'app-quote-status',
  template: `
    <span 
      class="lable" 
      [ngClass]="QuoteStatusUtils.getBorderClass(status)"
      [style.color]="getStatusColor()">
      {{ status }}
    </span>
  `,
  styles: [`
    .lable {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 0.875rem;
      font-weight: 500;
      border: 1px solid;
    }
  `]
})
export class QuoteStatusComponent {
  @Input() status: string = '';
  
  QuoteStatusUtils = QuoteStatusUtils;

  getStatusColor(): string {
    const colorMap: { [key: string]: string } = {
      'Pending': '#ffc107',
      'Sent': '#28a745',
      'To Invoice': '#17a2b8',
      'Revised': '#6c757d',
      'Canceled': '#dc3545',
      'Paid': '#28a745',
      'Awaiting Payment': '#fd7e14',
      'Expired': '#dc3545'
    };
    return colorMap[this.status] || '#6c757d';
  }
}
