import { Injectable, ViewChild} from '@angular/core';
import Swal from 'sweetalert2';

//Components
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';

@Injectable({
  providedIn: 'root'
})
export class SwalAlertsService {
    @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
    @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
    private audio!: HTMLAudioElement;
  constructor() { }


  // Reusable alerts

  showSuccessDialog(title: string, message: string, callback?: () => void) {
    Swal.fire({
      title,
      text: message,
      icon: 'success',
      confirmButtonText: 'OK',
      confirmButtonColor: '#28a745',
    }).then((result) => {
      if (result.isConfirmed && callback) {
        callback();
      }
    });
  }

  showWarning(message: string, callback: () => void) {
    Swal.fire({
      title: 'Warning!',
      text: message,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'OK',
      confirmButtonColor: '#ff7e5f',
      cancelButtonText: "Cancel",
      cancelButtonColor: "#be0032",
     
      footer: '<a href="#" id="custom-link" style="background-color: #007bff; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none;">Ask Chimp</a>'
    }).then((result) => {
      if (result.isConfirmed) {
        callback();
      }

      // Directly handle the "Ask Chimp" link click event after Swal is closed
      if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
        this.handleChatBotError(message);
      }
    });

  }


  showWarningWithCancel(message: string, onConfirm: () => void, onCancel?: () => void) {
  Swal.fire({
    title: 'Warning!',
    text: message,
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: 'OK',
    confirmButtonColor: '#ff7e5f',
    cancelButtonText: 'Cancel',
    cancelButtonColor: '#be0032',
    footer: `
      <a href="#" id="custom-link"
         style="background-color: #007bff; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none;">
         Ask Chimp
      </a>`
  }).then((result) => {
    if (result.isConfirmed) {
      onConfirm();
    } else if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
      this.handleChatBotError(message);
      if (onCancel) {
        onCancel(); 
      }
    }
  });
}


  attachChatBotHandler(message: string) {
    setTimeout(() => {
      document.getElementById('custom-link')?.addEventListener('click', (event) => {
        event.preventDefault();
        this.handleChatBotError(message);
      });
    }, 100);
  }

  // Extracted error dialog handling to a separate method with dynamic message
  showErrorDialog(message: string) {
    Swal.fire({
      title: "Error!",
      text: message,
      icon: "error",
      showCancelButton: true,
      confirmButtonText: "OK",
      cancelButtonText: "Ask Chimp",
      confirmButtonColor: "#be0032",
      cancelButtonColor: "#007bff",
    }).then((result) => {
      if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
        this.handleChatBotError(message);
      }
    });
  }

  showConfirmationDialog(title: string, message: string, onConfirm: () => void, oncancel?: () => void) {
    Swal.fire({
      title,
      text: message,
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: 'Yes',
      confirmButtonColor: '#ff7e5f',
      cancelButtonText: 'No',
      cancelButtonColor: '#be0032',
    }).then((result) => {
      if (result.isConfirmed) {
        onConfirm();
      } else if (result.dismiss === Swal.DismissReason.cancel && oncancel) {
        oncancel();
      }
    });
  }
  
  handleChatBotError(message: string) {
    if (!this.chatBotComponent) {
      console.error("ChatBotComponent is not available.");
      return;
    }

    Swal.fire({
      title: "Processing...",
      text: "Please wait while Chimp processes your request.",
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
        this.chatBotComponent.setInputData(message);
        this.chatBotComponent.responseReceived.subscribe((response) => {
          Swal.close();
          this.chatResponseComponent.showPopup = true;
          this.chatResponseComponent.responseData = response;
          this.playLoadingSound();
          this.stopLoadingSound();
        });
      },
    });
  }

  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }


  
    showError(title: string, text: string, chatbotInput?: string): void {
      Swal.fire({
        title,
        text,
        icon: 'error',
        confirmButtonText: 'OK',
        cancelButtonText: 'Ask Chimp',
        confirmButtonColor: '#be0032',
        cancelButtonColor: '#007bff',
        showCancelButton: chatbotInput ? true : false,
      }).then(result => {
        if (chatbotInput && result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
          this.askChimp(chatbotInput);
        }
      });
    }
    
    askChimp(input: string): void {
      if (!this.chatBotComponent) {
        console.error('ChatBotComponent is not available.');
        return;
      }
    
      Swal.fire({
        title: 'Processing...',
        text: 'Please wait while Chimp processes your request.',
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading();
          this.chatBotComponent.setInputData(input);
          this.chatBotComponent.responseReceived.subscribe(response => {
            Swal.close();
            this.chatResponseComponent.showPopup = true;
            this.chatResponseComponent.responseData = response;
            this.playLoadingSound();
            this.stopLoadingSound();
          });
        },
      });
    }




    showErrorWithChimpSupport(message: string, chatbotPrompt: string) {
  return Swal.fire({
    title: 'Error!',
    text: message,
    icon: 'error',
    confirmButtonText: 'OK',
    cancelButtonText: 'Ask Chimp',
    confirmButtonColor: '#be0032',
    cancelButtonColor: '#007bff',
    showCancelButton: true,
  }).then((result) => {
    if (
      result.isDismissed &&
      result.dismiss === Swal.DismissReason.cancel
    ) {
      this.processWithChatbot(chatbotPrompt);
    }
  });
}

processWithChatbot(prompt: string) {
  Swal.fire({
    title: 'Processing...',
    text: 'Please wait while Chimp processes your request.',
    allowOutsideClick: false,
    didOpen: () => {
      Swal.showLoading();
      this.chatBotComponent.setInputData(prompt);
      this.chatBotComponent.responseReceived.subscribe((response) => {
        Swal.close();
        this.chatResponseComponent.showPopup = true;
        this.chatResponseComponent.responseData = response;
        this.playLoadingSound();
        this.stopLoadingSound();
      });
    },
  });
}


showSwalWarning(title: string, text: string, chatbotInput: string): void {
  Swal.fire({
    title,
    text,
    icon: 'warning',
    confirmButtonText: 'OK',
    cancelButtonText: 'Ask Chimp',
    confirmButtonColor: '#ff7e5f',
    cancelButtonColor: '#be0032',
    showCancelButton: true,
  }).then(result => {
    if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
      if (this.chatBotComponent) {
        Swal.fire({
          title: 'Processing...',
          text: 'Please wait while Chimp processes your request.',
          allowOutsideClick: false,
          didOpen: () => {
            Swal.showLoading();
            this.chatBotComponent.setInputData(chatbotInput);
            this.chatBotComponent.responseReceived.subscribe(response => {
              Swal.close();
              this.chatResponseComponent.showPopup = true;
              this.chatResponseComponent.responseData = response;
              this.playLoadingSound();
              this.stopLoadingSound();
            });
          },
        });
      } else {
        console.error('ChatBotComponent is not available.');
      }
    }
  });
}


}
