import { Component, Input, Output, EventEmitter } from '@angular/core';
import { QuoteStatusUtils } from '../../quotation.constants';
import { QuoteHead } from '../../quotation';

@Component({
  selector: 'app-quote-actions',
  template: `
    <div class="action-buttons">
      <!-- View Button -->
      <button 
        (click)="onView()"
        [disabled]="QuoteStatusUtils.isViewDisabled(quote.status)"
        class="btn btn-orange btn-sm action-btn" 
        title="View"
        [attr.aria-label]="'View quote ' + quote.quoteNumber">
        <i class="bi bi-arrows-fullscreen"
          [style.color]="QuoteStatusUtils.isViewDisabled(quote.status) ? '#aaa' : '#4262ff'">
        </i>
      </button>

      <!-- Preview Button -->
      <button 
        class="btn btn-warning btn-sm action-btn"
        data-bs-target="#quotePreviewModal"
        data-bs-toggle="modal" 
        (click)="onPreview()"
        [disabled]="QuoteStatusUtils.isPreviewDisabled(quote.status)"
        title="Preview"
        [attr.aria-label]="'Preview quote ' + quote.quoteNumber">
        <i class="bi bi-eye"
          [style.color]="QuoteStatusUtils.isPreviewDisabled(quote.status) ? '#aaa' : '#debe15'">
        </i>
      </button>

      <!-- Delete Button -->
      <button 
        (click)="onDelete()" 
        class="btn btn-danger btn-sm action-btn"
        title="Delete"
        [attr.aria-label]="'Delete quote ' + quote.quoteNumber">
        <i class="ri-delete-bin-line" style="color: #FF0000;"></i>
      </button>
    </div>
  `,
  styles: [`
    .action-buttons {
      display: flex;
      gap: 2px;
      justify-content: center;
      align-items: center;
    }
    
    .action-btn {
      border: none;
      background: none;
      padding: 2px;
      font-size: 1rem;
      cursor: pointer;
      transition: opacity 0.2s ease;
    }
    
    .action-btn:hover:not(:disabled) {
      opacity: 0.8;
    }
    
    .action-btn:disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }
  `]
})
export class QuoteActionsComponent {
  @Input() quote!: QuoteHead;
  @Input() index!: number;
  
  @Output() view = new EventEmitter<number>();
  @Output() preview = new EventEmitter<number>();
  @Output() delete = new EventEmitter<{id: number, index: number}>();
  
  QuoteStatusUtils = QuoteStatusUtils;

  onView(): void {
    if (!QuoteStatusUtils.isViewDisabled(this.quote.status)) {
      this.view.emit(this.quote.quoteId);
    }
  }

  onPreview(): void {
    if (!QuoteStatusUtils.isPreviewDisabled(this.quote.status)) {
      this.preview.emit(this.quote.quoteId);
    }
  }

  onDelete(): void {
    this.delete.emit({ id: this.quote.quoteId, index: this.index });
  }
}
